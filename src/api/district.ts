import request from './request'
import type { ResultData } from './request'

// 区域数据接口定义
export interface DistrictData {
  districtId: string
  code: string
  name: string
  longitude: number | null
  latitude: number | null
  level: number
  parentId: string | null
  children?: DistrictData[]
}

// 区域街道数据接口定义
export interface DistrictStreetData {
  id: string
  districtCode: string
  districtName: string
  longitude: string
  latitude: string
  level: number
  parentId: string | null
  hotCommunity?: {
    id: string
    districtCode: string
    districtName: string
    longitude: string
    latitude: string
    level: number
    parentId: string
  }
  path?: string
}

// 首字母区域数据接口定义
export interface HeadCharDistrictData {
  districtId: string
  code: string
  name: string
  longitude: number | null
  latitude: number | null
  level: number
  parentId: string | null
  headChar: string
  hotFlags: string
  useCount: number | null
  lifecircleUseCount: number | null
  subjectTypes: string | null
  pid: string
  communityLineUseCount: number | null
}

/**
 * @name 区域管理模块
 */

// 获取所有区域街道数据
export const getAllDistrictStreetApi = () => {
  return request.get<{
    district: DistrictStreetData[]
    hotCommunity: DistrictStreetData[]
  }>('/applet/district/getAllDistrictStreet')
}

// 根据首字母获取区域数据
export const getDistrictByHeadCharApi = (name: string) => {
  return request.get<HeadCharDistrictData[]>('/applet/district/getDistrictByHeadChar', { name })
}

// 根据区域代码获取区域详情
export const getDistrictByCodeApi = (code: string) => {
  return request.get<DistrictData>('/applet/district/getByCode', { code })
}

// 获取区域层级数据
export const getDistrictHierarchyApi = (parentId?: string) => {
  return request.get<DistrictData[]>('/applet/district/hierarchy', { parentId })
}
